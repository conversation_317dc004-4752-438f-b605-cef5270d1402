# -*- coding: utf-8 -*-

# =================================================================================================
# 语言、文本、描述资源库
# =================================================================================================

# -------------------------------------------------------------------------------------------------
# 效果名称映射
# -------------------------------------------------------------------------------------------------
# 将代码中的效果ID映射为玩家可读的、更具画面感的中文名称
EFFECT_NAME_MAP = {
    "fear": "恐惧",
    "vulnerable": "易伤",
    "weak": "虚弱",
    "strength": "力量",
    "dexterity": "敏捷",
    "bleeding": "流血",
    "healing": "治愈",
    "draw_down": "抽牌弱化",
    "discard_lock": "弃牌锁定",
    "wet": "湿漉漉",
    "arousal": "发情",
    "resistance_down": "抵抗减弱",
    "submission": "屈服",
    "clarity": "清醒",
    "evasion": "闪避",
    "focus": "专注",
    "fear": "恐惧",
    "provocation": "挑衅",
    "cat_nip_high": "猫薄荷快感",
    "yarn_ball_distraction": "毛线球分心",
}

# -------------------------------------------------------------------------------------------------
# 游戏开场与过场
# -------------------------------------------------------------------------------------------------

def get_opening_prologue(cat_girl_name, location):
    """
    生成游戏开场的追逐场景文本。
    :param cat_girl_name: 猫娘的名字
    :param location: 场景地点
    :return: 逐字显示的开场文本
    """
    return f"在{location}，你与{cat_girl_name}的追逐开始了..."

def get_phase_two_transition_text(cat_girl_name):
    """
    生成进入第二阶段时的过场文本。
    :param cat_girl_name: 猫娘的名字
    :return: 逐字显示的过场文本
    """
    # 这里的文本将会在后续版本中，根据玩家的行为和猫娘的性格进行动态调整
    # 目前只是一个初步的、更具挑逗性的版本
    return f"{cat_girl_name}的呼吸变得急促而滚烫，她的眼神开始迷离，身体不自觉地瘫软下来... 抵抗的意志正在被更原始的欲望所取代。游戏，进入了新的阶段。"

# -------------------------------------------------------------------------------------------------
# 卡牌描述
# -------------------------------------------------------------------------------------------------
# 我���将在这里为所有卡牌添加生动的描述
CARD_DESCRIPTIONS = {
    "湿漉漉的眼神": "一回合内，你施加的所有debuff效果增加1。",
    "尾随": "造成4点额外伤害。这次伤害独立计算，可以触发两次攻击特效。",
    # ... 其他卡牌描述将在这里添加
}

# -------------------------------------------------------------------------------------------------
# 战斗细节与回合结束文本 (第二阶段)
# -------------------------------------------------------------------------------------------------
# 这是核心。这里的文本将极尽详细、露骨之所能。
# 每一段文本都将是一个模板，可以通过填充身体部位、动作、反应等参数，动态生成。

PHASE_TWO_END_OF_TURN_TEMPLATES = [
    "你抓住{cat_girl_name}抵抗力量减弱的间隙，粗糙的手掌探入她汗湿的短裙，揉捏着她紧绷的臀瓣。她发出一声压抑的惊叫，身体在你掌下微微颤抖。",
    "你俯下身，将脸埋在{cat_girl_name}的颈窝间，贪婪地嗅着她身上混杂着奶香与惊恐的气味。你的手指顺着她纤细的脊柱一路向下，解开了她内衣的搭扣。",
    "“不...不要碰那里...” {cat_girl_name}的抵抗显得苍白无力。你的手指已经找到了那处湿润的缝隙，毫不犹豫地探了进��，感受着内部的紧致与灼热。",
    "你抓住她的脚踝，将她的一条腿扛在肩上，以一个屈辱的姿势将她彻底打开。你欣赏着她因为羞耻和恐惧而涨红的脸蛋，另一只手则开始玩弄她胸前颤抖的蓓蕾。",
    "你掐住{cat_girl_name}的下巴，强迫她看着你的眼睛。你看到那双美丽的瞳孔中，倒映着你自己充满欲望的丑陋面孔。你低头吻了上去，舌头撬开她的贝齿，品尝着她口中的津液。",
    # 更多的模板将会被添加，包括更直接的插入、内射等描写
]

def get_random_phase_two_action_text(cat_girl_name):
    """
    从模板库中随机选择一个，并生成回合结束时的行动描述。
    :param cat_girl_name: 猫娘的名字
    :return: 一段详细的行动描述
    """
    import random
    template = random.choice(PHASE_TWO_END_OF_TURN_TEMPLATES)
    # 在未来，这里可以加入更多参数，如身体部位、玩家状态等，让文本更加动态
    return template.format(cat_girl_name=cat_girl_name)
