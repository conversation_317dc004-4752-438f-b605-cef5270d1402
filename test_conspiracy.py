#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试阴谋机制的简单脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from nekopara_pve import Game, Entity, CONSPIRACY_CARDS, PLAYER_COMBAT_DECK

def test_conspiracy_system():
    """测试阴谋系统的基本功能"""
    print("=== 阴谋机制测试 ===")

    # 创建游戏实例
    game = Game()
    game.turn = 1

    # 创建测试用的玩家和敌人
    game.player = Entity("测试玩家", 100, PLAYER_COMBAT_DECK, is_player=True)
    game.enemy = Entity("测试敌人", 80, [], is_player=False)

    print("1. 测试阴谋启动...")

    # 测试玩家麻醉剂阴谋
    game.player.add_conspiracy("麻醉剂", game.turn)
    print(f"玩家阴谋状态: {game.player.conspiracies}")
    print(f"阴谋启动回合: {game.player.conspiracy_start_turn}")
    assert game.player.has_conspiracy("麻醉剂"), "麻醉剂阴谋应该已启动"

    # 测试敌人大声呼救阴谋
    game.enemy.add_conspiracy("大声呼救", game.turn)
    print(f"敌人阴谋状态: {game.enemy.conspiracies}")
    assert game.enemy.has_conspiracy("大声呼救"), "大声呼救阴谋应该已启动"

    print("✓ 阴谋启动测试通过")

    print("\n2. 测试阴谋摧毁...")

    # 测试阴谋摧毁
    game.player.remove_conspiracy("麻醉剂")
    print(f"摧毁后玩家阴谋状态: {game.player.conspiracies}")
    print(f"摧毁后阴谋启动回合: {game.player.conspiracy_start_turn}")
    assert not game.player.has_conspiracy("麻醉剂"), "麻醉剂阴谋应该已被摧毁"

    print("✓ 阴谋摧毁测试通过")

    print("\n3. 测试阴谋卡牌定义...")

    # 检查阴谋卡牌定义
    assert "麻醉剂" in CONSPIRACY_CARDS["player"], "玩家麻醉剂卡牌应该存在"
    assert "大声呼救" in CONSPIRACY_CARDS["enemy"], "敌人大声呼救卡牌应该存在"
    assert "手机报警" in CONSPIRACY_CARDS["enemy"], "敌人手机报警卡牌应该存在"

    print("✓ 阴谋卡牌定义测试通过")

    print("\n4. 测试费用显示格式...")

    # 测试费用显示格式
    cost_0 = game.format_cost(0)
    cost_3 = game.format_cost(3)
    print(f"费用0显示: {cost_0}")
    print(f"费用3显示: {cost_3}")
    assert cost_0 == "(-)", "费用0应该显示为(-)"
    assert cost_3 == "***", "费用3应该显示为***"

    print("✓ 费用显示格式测试通过")

    print("\n5. 测试连续未受伤回合数...")

    # 测试连续未受伤回合数
    game.enemy.turns_without_damage = 4
    print(f"敌人连续未受伤回合数: {game.enemy.turns_without_damage}")

    # 模拟受伤
    old_hp = game.enemy.hp
    game.enemy.take_damage(10, game)
    print(f"受伤后连续未受伤回合数: {game.enemy.turns_without_damage}")
    assert game.enemy.turns_without_damage == 0, "受伤后应该重置连续未受伤回合数"

    print("✓ 连续未受伤回合数测试通过")

    print("\n=== 所有测试通过！ ===")
    print("\n更新后的阴谋机制功能说明:")
    print("- 玩家麻醉剂阴谋: 从启动回合开始计算，持续5回合以上至少打出1张手牌时，打出擒抱牌触发")
    print("- 敌人大声呼救阴谋: 生命值40以上且玩家手牌有围观群众时立即触发")
    print("- 敌人手机报警阴谋: 连续3回合未受伤后获得护盾时立即触发")
    print("- 阴谋触发后立即检查游戏结束条件")
    print("- 费用显示改为星号格式，'费用'统一改为'注意力'")
    print("- 敌人牌库添加深呼吸卡，发情卡有特殊处理逻辑")

if __name__ == "__main__":
    test_conspiracy_system()
